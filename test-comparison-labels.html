<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Comparison Labels - Specific Period Names</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            margin: 8px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .period-type {
            font-weight: 500;
            color: #495057;
        }
        
        .comparison-label {
            font-weight: 600;
            color: #007bff;
        }
        
        .current-date {
            background-color: #e3f2fd;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }
        
        .old-vs-new {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .old-labels, .new-labels {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 2px solid #dee2e6;
        }
        
        .old-labels {
            border-color: #dc3545;
        }
        
        .new-labels {
            border-color: #28a745;
        }
        
        .section-title {
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .old-labels .section-title {
            color: #dc3545;
        }
        
        .new-labels .section-title {
            color: #28a745;
        }
        
        .label-item {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            background-color: #f8f9fa;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Comparison Labels Test - Specific Period Names</h1>
        <div class="current-date" id="current-date"></div>
        
        <div class="test-title">New Dynamic Comparison Labels</div>
        <div id="test-results"></div>
        
        <div class="old-vs-new">
            <div class="old-labels">
                <div class="section-title">❌ Old Generic Labels</div>
                <div class="label-item">Compared to last month</div>
                <div class="label-item">Compared to month before last</div>
                <div class="label-item">Compared to last year</div>
                <div class="label-item">Compared to year before last</div>
            </div>
            
            <div class="new-labels">
                <div class="section-title">✅ New Specific Labels</div>
                <div id="new-labels-list"></div>
            </div>
        </div>
    </div>

    <!-- Load timezone utility for Pacific Time -->
    <script src="utils/timezone.js"></script>
    
    <script>
        /**
         * Enhanced getComparisonLabel function with specific period names
         * (Same implementation as in dashboard.js)
         */
        function getComparisonLabel(period) {
            // Get current date in Pacific Time (fallback to local time if timezone utility not available)
            const currentDate = window.SnapTimezone ? window.SnapTimezone.getPacificTime() : new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth(); // 0-based month
            
            // Calculate specific previous periods and format labels
            switch (period) {
                case 'currentMonth': {
                    // Compare current month to previous month
                    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
                    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
                    const prevDate = new Date(prevYear, prevMonth, 1);
                    const monthName = prevDate.toLocaleDateString('en-US', { month: 'long' });
                    return `Compared to ${monthName} ${prevYear}`;
                }
                
                case 'lastMonth': {
                    // Compare last month to month before last month (2 months back from current)
                    let monthsBack = 2;
                    let targetMonth = currentMonth - monthsBack;
                    let targetYear = currentYear;
                    
                    // Handle year boundary crossing
                    while (targetMonth < 0) {
                        targetMonth += 12;
                        targetYear -= 1;
                    }
                    
                    const targetDate = new Date(targetYear, targetMonth, 1);
                    const monthName = targetDate.toLocaleDateString('en-US', { month: 'long' });
                    return `Compared to ${monthName} ${targetYear}`;
                }
                
                case 'currentYear': {
                    // Compare current year to previous year
                    const prevYear = currentYear - 1;
                    return `Compared to ${prevYear}`;
                }
                
                case 'lastYear': {
                    // Compare last year to year before last year
                    const yearBeforeLast = currentYear - 2;
                    return `Compared to ${yearBeforeLast}`;
                }
                
                default:
                    return 'Compared to previous period';
            }
        }
        
        // Test the function
        function runTests() {
            const periods = ['currentMonth', 'lastMonth', 'currentYear', 'lastYear'];
            const resultsContainer = document.getElementById('test-results');
            const newLabelsContainer = document.getElementById('new-labels-list');
            
            // Display current date
            const currentDate = window.SnapTimezone ? window.SnapTimezone.getPacificTime() : new Date();
            document.getElementById('current-date').textContent = 
                `Current Date: ${currentDate.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                })} ${window.SnapTimezone ? '(Pacific Time)' : '(Local Time)'}`;
            
            periods.forEach(period => {
                const label = getComparisonLabel(period);
                
                // Add to test results
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-result';
                resultDiv.innerHTML = `
                    <span class="period-type">${period}:</span>
                    <span class="comparison-label">${label}</span>
                `;
                resultsContainer.appendChild(resultDiv);
                
                // Add to new labels list
                const labelDiv = document.createElement('div');
                labelDiv.className = 'label-item';
                labelDiv.textContent = label;
                newLabelsContainer.appendChild(labelDiv);
            });
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Edge Cases - Comparison Labels</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-scenario {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background-color: #f8f9fa;
        }
        
        .scenario-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 12px;
        }
        
        .test-result {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 12px;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            background-color: white;
            border-left: 3px solid #007bff;
        }
        
        .period-type {
            font-weight: 500;
            color: #6c757d;
        }
        
        .comparison-label {
            font-weight: 600;
            color: #007bff;
        }
        
        .current-date {
            background-color: #e3f2fd;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 12px;
            font-weight: 500;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Edge Cases Test - Comparison Labels</h1>
        <p>Testing year boundary transitions and edge cases for comparison label generation.</p>
        
        <div id="test-scenarios"></div>
    </div>

    <script>
        /**
         * Enhanced getComparisonLabel function with specific period names
         * Modified to accept a custom current date for testing
         */
        function getComparisonLabel(period, customCurrentDate = null) {
            // Use custom date for testing or current date
            const currentDate = customCurrentDate || new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth(); // 0-based month
            
            // Calculate specific previous periods and format labels
            switch (period) {
                case 'currentMonth': {
                    // Compare current month to previous month
                    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
                    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
                    const prevDate = new Date(prevYear, prevMonth, 1);
                    const monthName = prevDate.toLocaleDateString('en-US', { month: 'long' });
                    return `Compared to ${monthName} ${prevYear}`;
                }
                
                case 'lastMonth': {
                    // Compare last month to month before last month (2 months back from current)
                    let monthsBack = 2;
                    let targetMonth = currentMonth - monthsBack;
                    let targetYear = currentYear;
                    
                    // Handle year boundary crossing
                    while (targetMonth < 0) {
                        targetMonth += 12;
                        targetYear -= 1;
                    }
                    
                    const targetDate = new Date(targetYear, targetMonth, 1);
                    const monthName = targetDate.toLocaleDateString('en-US', { month: 'long' });
                    return `Compared to ${monthName} ${targetYear}`;
                }
                
                case 'currentYear': {
                    // Compare current year to previous year
                    const prevYear = currentYear - 1;
                    return `Compared to ${prevYear}`;
                }
                
                case 'lastYear': {
                    // Compare last year to year before last year
                    const yearBeforeLast = currentYear - 2;
                    return `Compared to ${yearBeforeLast}`;
                }
                
                default:
                    return 'Compared to previous period';
            }
        }
        
        // Test scenarios with different dates
        function runEdgeCaseTests() {
            const testScenarios = [
                {
                    title: "January 2025 (Current Month = January)",
                    date: new Date(2025, 0, 15), // January 15, 2025
                    description: "Tests January rolling back to December of previous year"
                },
                {
                    title: "February 2025 (Current Month = February)", 
                    date: new Date(2025, 1, 15), // February 15, 2025
                    description: "Tests February last month comparison to December"
                },
                {
                    title: "March 2025 (Current Month = March)",
                    date: new Date(2025, 2, 15), // March 15, 2025
                    description: "Tests normal month transitions"
                },
                {
                    title: "December 2025 (Current Month = December)",
                    date: new Date(2025, 11, 15), // December 15, 2025
                    description: "Tests end-of-year scenarios"
                },
                {
                    title: "July 2025 (Current Month = July)",
                    date: new Date(2025, 6, 15), // July 15, 2025
                    description: "Tests mid-year scenarios"
                }
            ];
            
            const container = document.getElementById('test-scenarios');
            const periods = ['currentMonth', 'lastMonth', 'currentYear', 'lastYear'];
            
            testScenarios.forEach(scenario => {
                const scenarioDiv = document.createElement('div');
                scenarioDiv.className = 'test-scenario';
                
                const titleDiv = document.createElement('div');
                titleDiv.className = 'scenario-title';
                titleDiv.textContent = scenario.title;
                scenarioDiv.appendChild(titleDiv);
                
                const currentDateDiv = document.createElement('div');
                currentDateDiv.className = 'current-date';
                currentDateDiv.textContent = `Current Date: ${scenario.date.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                })}`;
                scenarioDiv.appendChild(currentDateDiv);
                
                const descDiv = document.createElement('div');
                descDiv.style.marginBottom = '12px';
                descDiv.style.fontStyle = 'italic';
                descDiv.style.color = '#6c757d';
                descDiv.textContent = scenario.description;
                scenarioDiv.appendChild(descDiv);
                
                periods.forEach(period => {
                    const label = getComparisonLabel(period, scenario.date);
                    
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'test-result';
                    resultDiv.innerHTML = `
                        <span class="period-type">${period}:</span>
                        <span class="comparison-label">${label}</span>
                    `;
                    scenarioDiv.appendChild(resultDiv);
                });
                
                container.appendChild(scenarioDiv);
            });
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runEdgeCaseTests);
    </script>
</body>
</html>
